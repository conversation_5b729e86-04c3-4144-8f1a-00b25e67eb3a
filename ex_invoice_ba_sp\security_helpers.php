<?php
/**
 * Security Helper Functions for Ex Invoice BA SP
 * 
 * This file contains reusable security functions to prevent common vulnerabilities
 * and improve code quality across the application.
 * 
 * PHP Version: 5.2+ compatible
 */

/**
 * Validate and sanitize numeric input
 * 
 * @param mixed $value The value to validate
 * @param int $min Minimum allowed value (default: 1)
 * @param int $max Maximum allowed value (default: 999999)
 * @return int|false Returns sanitized integer or false if invalid
 */
function validateNumericInput($value, $min = 1, $max = 999999) {
    if (!isset($value) || !is_numeric($value)) {
        return false;
    }
    
    $intValue = (int)$value;
    if ($intValue < $min || $intValue > $max) {
        return false;
    }
    
    return $intValue;
}

/**
 * Validate session data
 * 
 * @param array $required_keys Array of required session keys
 * @return array|false Returns validated session data or false if invalid
 */
function validateSessionData($required_keys = array('user_id', 'user_name', 'user_org')) {
    $session_data = array();
    
    foreach ($required_keys as $key) {
        if (!isset($_SESSION[$key]) || empty($_SESSION[$key])) {
            error_log("Missing or empty session key: " . $key);
            return false;
        }
        $session_data[$key] = $_SESSION[$key];
    }
    
    return $session_data;
}

/**
 * Execute prepared statement with error handling
 * 
 * @param resource $conn Database connection
 * @param string $sql SQL query with placeholders
 * @param array $bind_params Associative array of bind parameters
 * @return resource|false Returns statement resource or false on error
 */
function executePreparedStatement($conn, $sql, $bind_params = array()) {
    $stmt = oci_parse($conn, $sql);
    if (!$stmt) {
        $error = oci_error($conn);
        error_log('Database parse error: ' . $error['message']);
        return false;
    }
    
    // Bind parameters
    foreach ($bind_params as $param_name => $param_value) {
        if (!oci_bind_by_name($stmt, $param_name, $param_value)) {
            $error = oci_error($stmt);
            error_log('Database bind error: ' . $error['message']);
            oci_free_statement($stmt);
            return false;
        }
    }
    
    // Execute statement
    if (!oci_execute($stmt)) {
        $error = oci_error($stmt);
        error_log('Database execute error: ' . $error['message']);
        oci_free_statement($stmt);
        return false;
    }
    
    return $stmt;
}

/**
 * Safely fetch array data with default values
 * 
 * @param array $array The array to fetch from
 * @param string $key The key to fetch
 * @param mixed $default Default value if key doesn't exist
 * @return mixed The value or default
 */
function safeArrayGet($array, $key, $default = '') {
    return isset($array[$key]) ? $array[$key] : $default;
}

/**
 * Validate SAP connection and function
 * 
 * @param object $sap SAP connection object
 * @param string $function_name SAP function name
 * @return object|false Returns SAP function object or false on error
 */
function validateSAPFunction($sap, $function_name) {
    if ($sap->GetStatus() != SAPRFC_OK) {
        error_log('SAP Connection failed: ' . $sap->PrintStatus());
        $sap->Close();
        return false;
    }
    
    $fce = $sap->NewFunction($function_name);
    if ($fce == false) {
        error_log('SAP Function creation failed for ' . $function_name . ': ' . $sap->PrintStatus());
        $sap->Close();
        return false;
    }
    
    return $fce;
}

/**
 * Get user organization mapping
 * 
 * @param string $user_org User organization code
 * @return string|false Returns mapped username or false if not found
 */
function getUserOrgMapping($user_org) {
    $user_mapping = array(
        '7000' => 'AFRKS24249',
        '5000' => 'SAR0007704'
    );
    
    return isset($user_mapping[$user_org]) ? $user_mapping[$user_org] : false;
}

/**
 * Get email configuration for organization
 * 
 * @param string $user_org User organization code
 * @return array|false Returns email config array or false if not found
 */
function getEmailConfig($user_org) {
    $email_config = array(
        '7000' => array(
            'to' => '<EMAIL>',
            'cc' => '<EMAIL>'
        ),
        '5000' => array(
            'to' => '<EMAIL>',
            'cc' => '<EMAIL>'
        )
    );
    
    return isset($email_config[$user_org]) ? $email_config[$user_org] : false;
}

/**
 * Log security event
 * 
 * @param string $event_type Type of security event
 * @param string $message Event message
 * @param array $context Additional context data
 */
function logSecurityEvent($event_type, $message, $context = array()) {
    $log_message = "[SECURITY] " . $event_type . ": " . $message;
    if (!empty($context)) {
        $log_message .= " Context: " . json_encode($context);
    }
    error_log($log_message);
}

/**
 * Validate POST data array
 * 
 * @param array $required_fields Array of required field names
 * @param array $validation_rules Array of validation rules per field
 * @return array|false Returns validated data or false if validation fails
 */
function validatePostData($required_fields, $validation_rules = array()) {
    $validated_data = array();
    
    foreach ($required_fields as $field) {
        if (!isset($_POST[$field])) {
            logSecurityEvent('VALIDATION_FAILED', 'Missing required field: ' . $field);
            return false;
        }
        
        $value = $_POST[$field];
        
        // Apply validation rules if specified
        if (isset($validation_rules[$field])) {
            $rule = $validation_rules[$field];
            
            switch ($rule['type']) {
                case 'numeric':
                    $min = isset($rule['min']) ? $rule['min'] : 1;
                    $max = isset($rule['max']) ? $rule['max'] : 999999;
                    $value = validateNumericInput($value, $min, $max);
                    if ($value === false) {
                        logSecurityEvent('VALIDATION_FAILED', 'Invalid numeric value for field: ' . $field);
                        return false;
                    }
                    break;
                    
                case 'string':
                    $max_length = isset($rule['max_length']) ? $rule['max_length'] : 255;
                    if (strlen($value) > $max_length) {
                        logSecurityEvent('VALIDATION_FAILED', 'String too long for field: ' . $field);
                        return false;
                    }
                    $value = trim($value);
                    break;
                    
                case 'email':
                    if (!filter_var($value, FILTER_VALIDATE_EMAIL)) {
                        logSecurityEvent('VALIDATION_FAILED', 'Invalid email for field: ' . $field);
                        return false;
                    }
                    break;
            }
        }
        
        $validated_data[$field] = $value;
    }
    
    return $validated_data;
}

/**
 * Clean up database resources
 * 
 * @param array $statements Array of statement resources to free
 */
function cleanupDatabaseResources($statements) {
    foreach ($statements as $stmt) {
        if (is_resource($stmt)) {
            oci_free_statement($stmt);
        }
    }
}

/**
 * Safe string extraction with bounds checking
 * 
 * @param string $string Source string
 * @param int $start Start position
 * @param int $length Length to extract
 * @return string Extracted string or empty string if bounds exceeded
 */
function safeSubstring($string, $start, $length) {
    if (strlen($string) < ($start + $length)) {
        return '';
    }
    return substr($string, $start, $length);
}

/**
 * Initialize transaction
 * 
 * @param resource $conn Database connection
 * @return bool True on success, false on failure
 */
function beginTransaction($conn) {
    $stmt = oci_parse($conn, "BEGIN");
    if (!$stmt) {
        error_log('Failed to parse BEGIN transaction');
        return false;
    }
    
    $result = oci_execute($stmt, OCI_NO_AUTO_COMMIT);
    oci_free_statement($stmt);
    
    if (!$result) {
        error_log('Failed to begin transaction');
        return false;
    }
    
    return true;
}

/**
 * Commit or rollback transaction based on success flag
 * 
 * @param resource $conn Database connection
 * @param bool $success Whether operations were successful
 * @param string $context Context for logging
 */
function finalizeTransaction($conn, $success, $context = '') {
    if ($success) {
        if (oci_commit($conn)) {
            error_log("Transaction committed successfully" . ($context ? " for " . $context : ""));
        } else {
            error_log("Failed to commit transaction" . ($context ? " for " . $context : ""));
        }
    } else {
        if (oci_rollback($conn)) {
            error_log("Transaction rolled back" . ($context ? " for " . $context : ""));
        } else {
            error_log("Failed to rollback transaction" . ($context ? " for " . $context : ""));
        }
    }
}

?>
