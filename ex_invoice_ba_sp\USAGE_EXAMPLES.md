# Usage Examples - Security Helper Functions

## Overview
This document provides examples of how to use the security helper functions created during the refactoring process.

## Basic Usage Examples

### 1. Session Validation
```php
// Include the helper functions
require_once('security_helpers.php');

// Validate session data
$session_data = validateSessionData();
if ($session_data === false) {
    die('Error: Invalid session data');
}

$user_id = $session_data['user_id'];
$user_name = $session_data['user_name'];
$user_org = $session_data['user_org'];
```

### 2. Input Validation
```php
// Validate numeric input with custom range
$total = validateNumericInput($_POST['total'], 1, 1000);
if ($total === false) {
    die('Error: Invalid total parameter');
}

// Validate multiple POST fields
$validation_rules = array(
    'total' => array('type' => 'numeric', 'min' => 1, 'max' => 1000),
    'vendor_name' => array('type' => 'string', 'max_length' => 100),
    'email' => array('type' => 'email')
);

$validated_data = validatePostData(array('total', 'vendor_name', 'email'), $validation_rules);
if ($validated_data === false) {
    die('Error: Validation failed');
}
```

### 3. Database Operations with Prepared Statements
```php
// Execute a prepared statement safely
$sql = "SELECT * FROM EX_TRANS_HDR WHERE no_ba = :no_ba AND status = :status";
$bind_params = array(
    ':no_ba' => $no_ba,
    ':status' => 'ACTIVE'
);

$stmt = executePreparedStatement($conn, $sql, $bind_params);
if ($stmt === false) {
    die('Error: Database query failed');
}

// Process results
$results = array();
while ($row = oci_fetch_array($stmt)) {
    $results[] = array(
        'no_invoice' => safeArrayGet($row, 'NO_INVOICE'),
        'vendor' => safeArrayGet($row, 'VENDOR'),
        'amount' => safeArrayGet($row, 'AMOUNT', 0)
    );
}

// Clean up
cleanupDatabaseResources(array($stmt));
```

### 4. SAP Integration with Error Handling
```php
// Validate SAP connection and create function
$sap = new SAPConnection();
$sap->Connect($link_koneksi_sap);
$sap->Open();

$fce = validateSAPFunction($sap, "ZCFI_DOC_EXPEDITION_INVOICE");
if ($fce === false) {
    die('Error: SAP function creation failed');
}

// Use the function
$fce->P_EXPDS_TYPE = '4';
// ... set other parameters

$fce->Call();
if ($fce->GetStatus() == SAPRFC_OK) {
    // Process success
    echo "SAP call successful";
} else {
    error_log('SAP call failed: ' . $fce->PrintStatus());
}

// Clean up
$fce->Close();
$sap->Close();
```

### 5. Organization-based Configuration
```php
// Get user organization mapping
$usnam = getUserOrgMapping($user_org);
if ($usnam === false) {
    die('Error: Invalid user organization');
}

// Get email configuration
$email_config = getEmailConfig($user_org);
if ($email_config === false) {
    die('Error: No email configuration for organization');
}

$mailTo = $email_config['to'];
$mailCc = $email_config['cc'];
```

### 6. Transaction Management
```php
// Begin transaction
if (!beginTransaction($conn)) {
    die('Error: Could not begin transaction');
}

$success = true;
$statements = array();

try {
    // Perform multiple database operations
    $sql1 = "UPDATE EX_BA_INVOICE SET DIPAKAI = :dipakai WHERE NO_BA = :no_ba";
    $stmt1 = executePreparedStatement($conn, $sql1, array(':dipakai' => 0, ':no_ba' => $no_ba));
    if ($stmt1 === false) {
        $success = false;
    } else {
        $statements[] = $stmt1;
    }
    
    $sql2 = "INSERT INTO EX_BA_INVOICE (NO_BA, STATUS, CREATED_BY) VALUES (:no_ba, :status, :user_id)";
    $stmt2 = executePreparedStatement($conn, $sql2, array(':no_ba' => $no_ba, ':status' => '120', ':user_id' => $user_id));
    if ($stmt2 === false) {
        $success = false;
    } else {
        $statements[] = $stmt2;
    }
    
} catch (Exception $e) {
    error_log('Database operation failed: ' . $e->getMessage());
    $success = false;
}

// Clean up resources
cleanupDatabaseResources($statements);

// Finalize transaction
finalizeTransaction($conn, $success, 'ekspedisi_dokumen_bendahara');
```

### 7. Safe String Operations
```php
// Safe substring extraction
$expedition_number = safeSubstring($keter_eks, 13, 10);
if (empty($expedition_number)) {
    error_log('Could not extract expedition number from: ' . $keter_eks);
    $expedition_number = 'N/A';
}
```

### 8. Security Event Logging
```php
// Log security events with context
logSecurityEvent('INVALID_INPUT', 'Invalid no_ba parameter received', array(
    'user_id' => $user_id,
    'input_value' => $_POST['idke0'],
    'ip_address' => $_SERVER['REMOTE_ADDR']
));

logSecurityEvent('SAP_CONNECTION_FAILED', 'Could not connect to SAP system', array(
    'user_org' => $user_org,
    'function_name' => 'ZCFI_DOC_EXPEDITION_INVOICE'
));
```

## Complete Example: Refactored Case Structure

```php
case "ekspedisi_dokumen_bendahara":
    // Include security helpers
    require_once('security_helpers.php');
    
    // Validate session
    $session_data = validateSessionData();
    if ($session_data === false) {
        die('Error: Invalid session data');
    }
    
    // Validate input
    $total = validateNumericInput($_POST['total'], 1, 1000);
    if ($total === false) {
        logSecurityEvent('VALIDATION_FAILED', 'Invalid total parameter');
        die('Error: Invalid total parameter');
    }
    
    // Get organization configuration
    $usnam = getUserOrgMapping($session_data['user_org']);
    $email_config = getEmailConfig($session_data['user_org']);
    
    if ($usnam === false || $email_config === false) {
        die('Error: Invalid organization configuration');
    }
    
    // Initialize SAP connection
    $sap = new SAPConnection();
    $sap->Connect($link_koneksi_sap);
    $sap->Open();
    
    $fce = validateSAPFunction($sap, "ZCFI_DOC_EXPEDITION_INVOICE");
    if ($fce === false) {
        die('Error: SAP initialization failed');
    }
    
    // Begin transaction
    if (!beginTransaction($conn)) {
        $sap->Close();
        die('Error: Could not begin transaction');
    }
    
    $success = true;
    $statements = array();
    
    // Process each item
    for ($k = 0; $k < $total; $k++) {
        $idke = "idke" . $k;
        if (!isset($_POST[$idke])) {
            continue;
        }
        
        $no_ba = validateNumericInput($_POST[$idke]);
        if ($no_ba === false) {
            logSecurityEvent('INVALID_INPUT', 'Invalid no_ba value', array('value' => $_POST[$idke]));
            continue;
        }
        
        // Database operations with prepared statements
        $sql = "SELECT ETH.NO_INV_SAP, EI.NO_INVOICE FROM EX_TRANS_HDR ETH 
                LEFT JOIN EX_INVOICE EI ON EI.NO_INVOICE = ETH.NO_INVOICE 
                WHERE ETH.no_ba = :no_ba";
        
        $stmt = executePreparedStatement($conn, $sql, array(':no_ba' => $no_ba));
        if ($stmt === false) {
            $success = false;
            break;
        }
        
        $statements[] = $stmt;
        
        // Process results...
        while ($row = oci_fetch_array($stmt)) {
            $no_inv_sap = safeArrayGet($row, 'NO_INV_SAP');
            $no_invoice = safeArrayGet($row, 'NO_INVOICE');
            
            // Continue processing...
        }
    }
    
    // Clean up resources
    cleanupDatabaseResources($statements);
    
    // Finalize transaction
    finalizeTransaction($conn, $success, 'ekspedisi_dokumen_bendahara');
    
    // Clean up SAP
    $fce->Close();
    $sap->Close();
    
    break;
```

## Best Practices

1. **Always validate input** before using it in database queries or SAP calls
2. **Use prepared statements** for all database operations
3. **Clean up resources** after use to prevent memory leaks
4. **Log security events** for monitoring and debugging
5. **Use transactions** for operations that modify multiple tables
6. **Validate SAP connections** before making function calls
7. **Handle errors gracefully** without exposing sensitive information

## Error Handling Guidelines

1. **Log errors** with sufficient context for debugging
2. **Don't expose** database errors or SAP errors to users
3. **Use appropriate HTTP status codes** for different error types
4. **Provide user-friendly error messages** while logging technical details
5. **Clean up resources** even when errors occur

## Security Considerations

1. **Never trust user input** - always validate and sanitize
2. **Use parameterized queries** to prevent SQL injection
3. **Log security events** for monitoring
4. **Validate session data** before processing requests
5. **Use HTTPS** for sensitive operations
6. **Implement rate limiting** for API endpoints
7. **Regular security audits** of the codebase
